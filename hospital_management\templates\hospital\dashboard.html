{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Dashboard - {{ user_profile.role|title }}</h2>
    </div>
</div>

{% if user_profile.role == 'receptionist' %}
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5><i class="fas fa-users"></i> Total Patients</h5>
                <h3>{{ total_patients }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5><i class="fas fa-calendar"></i> Today's Appointments</h5>
                <h3>{{ today_appointments }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <a href="{% url 'register_patient' %}" class="btn btn-light">
                    <i class="fas fa-plus"></i> Register New Patient
                </a>
            </div>
        </div>
    </div>
</div>

{% elif user_profile.role == 'doctor' %}
<div class="row mt-4">
    <div class="col-md-12">
        <h4>My Upcoming Appointments</h4>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Patient</th>
                        <th>Date & Time</th>
                        <th>Illness</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for appointment in my_appointments %}
                    <tr>
                        <td>{{ appointment.patient.name }}</td>
                        <td>{{ appointment.appointment_date }}</td>
                        <td>{{ appointment.illness_description|truncatewords:10 }}</td>
                        <td>
                            <a href="{% url 'patient_details' appointment.patient.patient_id %}" 
                               class="btn btn-sm btn-primary">View Details</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="4">No upcoming appointments</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

{% elif user_profile.role == 'lab_technician' %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <h5><i class="fas fa-flask"></i> Pending Reports</h5>
                <h3>{{ pending_reports }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <a href="{% url 'add_lab_report' %}" class="btn btn-light">
                    <i class="fas fa-plus"></i> Add Lab Report
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}