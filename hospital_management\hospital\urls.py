from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('login/', views.user_login, name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('register-patient/', views.register_patient, name='register_patient'),
    path('receipt/<str:appointment_id>/', views.print_receipt, name='print_receipt'),
    path('doctor/appointments/', views.doctor_appointments, name='doctor_appointments'),
    path('patient/<str:patient_id>/', views.patient_details, name='patient_details'),
    path('lab-reports/', views.lab_reports, name='lab_reports'),
    path('add-lab-report/', views.add_lab_report, name='add_lab_report'),
]