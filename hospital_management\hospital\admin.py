from django.contrib import admin
from .models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Doctor, Patient, Appointment, LabReport

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'role', 'phone']
    list_filter = ['role']

@admin.register(Doctor)
class DoctorAdmin(admin.ModelAdmin):
    list_display = ['user', 'specialization', 'consultation_fee']

@admin.register(Patient)
class PatientAdmin(admin.ModelAdmin):
    list_display = ['patient_id', 'name', 'age', 'gender', 'created_at']
    search_fields = ['name', 'patient_id']

@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    list_display = ['appointment_id', 'patient', 'doctor', 'appointment_date', 'status']
    list_filter = ['status', 'doctor']

@admin.register(LabReport)
class LabReportAdmin(admin.ModelAdmin):
    list_display = ['report_id', 'patient', 'test_name', 'created_at']