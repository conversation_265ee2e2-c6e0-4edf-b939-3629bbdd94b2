{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-user-plus"></i> Register New Patient</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Patient Name</label>
                                {{ form.name }}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Age</label>
                                {{ form.age }}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Gender</label>
                                {{ form.gender }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Phone</label>
                                {{ form.phone }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Doctor</label>
                                {{ form.doctor }}
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Address</label>
                        {{ form.address }}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Illness Description</label>
                        {{ form.illness_description }}
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Register Patient
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}