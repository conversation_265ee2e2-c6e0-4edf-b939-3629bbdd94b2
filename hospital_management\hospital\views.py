from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import login, authenticate
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import get_template
from .models import <PERSON><PERSON>, <PERSON><PERSON>oint<PERSON>, <PERSON>, LabReport, UserProfile
from .forms import PatientRegistrationForm, AppointmentForm, LabReportForm
from django.db.models import Q

def user_login(request):
    if request.method == 'POST':
        username = request.POST['username']
        password = request.POST['password']
        user = authenticate(request, username=username, password=password)
        if user:
            login(request, user)
            return redirect('dashboard')
        messages.error(request, 'Invalid credentials')
    return render(request, 'registration/login.html')

@login_required
def dashboard(request):
    user_profile = UserProfile.objects.get(user=request.user)
    context = {'user_profile': user_profile}
    
    if user_profile.role == 'receptionist':
        context['total_patients'] = Patient.objects.count()
        context['today_appointments'] = Appointment.objects.filter(
            appointment_date__date=timezone.now().date()
        ).count()
    elif user_profile.role == 'doctor':
        doctor = Doctor.objects.get(user=request.user)
        context['my_appointments'] = Appointment.objects.filter(
            doctor=doctor, status='scheduled'
        ).order_by('appointment_date')
    elif user_profile.role == 'lab_technician':
        context['pending_reports'] = LabReport.objects.filter(
            test_results__isnull=True
        ).count()
    
    return render(request, 'hospital/dashboard.html', context)

@login_required
def register_patient(request):
    if request.user.userprofile.role != 'receptionist':
        messages.error(request, 'Access denied')
        return redirect('dashboard')
    
    if request.method == 'POST':
        form = PatientRegistrationForm(request.POST)
        if form.is_valid():
            patient = form.save()
            
            # Create appointment
            appointment = Appointment.objects.create(
                patient=patient,
                doctor=form.cleaned_data['doctor'],
                illness_description=form.cleaned_data['illness_description'],
                fee=form.cleaned_data['doctor'].consultation_fee,
                created_by=request.user
            )
            
            messages.success(request, f'Patient registered successfully! Patient ID: {patient.patient_id}')
            return redirect('print_receipt', appointment_id=appointment.appointment_id)
    else:
        form = PatientRegistrationForm()
    
    return render(request, 'hospital/register_patient.html', {'form': form})

@login_required
def print_receipt(request, appointment_id):
    appointment = get_object_or_404(Appointment, appointment_id=appointment_id)
    return render(request, 'hospital/receipt.html', {'appointment': appointment})

@login_required
def doctor_appointments(request):
    if request.user.userprofile.role != 'doctor':
        messages.error(request, 'Access denied')
        return redirect('dashboard')
    
    doctor = Doctor.objects.get(user=request.user)
    appointments = Appointment.objects.filter(doctor=doctor).order_by('-appointment_date')
    return render(request, 'hospital/doctor_appointments.html', {'appointments': appointments})

@login_required
def patient_details(request, patient_id):
    patient = get_object_or_404(Patient, patient_id=patient_id)
    appointments = Appointment.objects.filter(patient=patient)
    lab_reports = LabReport.objects.filter(patient=patient)
    
    context = {
        'patient': patient,
        'appointments': appointments,
        'lab_reports': lab_reports
    }
    return render(request, 'hospital/patient_details.html', context)

@login_required
def lab_reports(request):
    if request.user.userprofile.role not in ['lab_technician', 'doctor']:
        messages.error(request, 'Access denied')
        return redirect('dashboard')
    
    if request.user.userprofile.role == 'doctor':
        doctor = Doctor.objects.get(user=request.user)
        reports = LabReport.objects.filter(appointment__doctor=doctor)
    else:
        reports = LabReport.objects.all()
    
    return render(request, 'hospital/lab_reports.html', {'reports': reports})

@login_required
def add_lab_report(request):
    if request.user.userprofile.role != 'lab_technician':
        messages.error(request, 'Access denied')
        return redirect('dashboard')
    
    if request.method == 'POST':
        form = LabReportForm(request.POST, request.FILES)
        if form.is_valid():
            report = form.save(commit=False)
            report.created_by = request.user
            report.save()
            messages.success(request, 'Lab report added successfully!')
            return redirect('lab_reports')
    else:
        form = LabReportForm()
    
    return render(request, 'hospital/add_lab_report.html', {'form': form})